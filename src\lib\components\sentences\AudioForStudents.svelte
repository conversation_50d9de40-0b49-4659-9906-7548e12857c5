<script lang="ts">
	import BaseButton from '../common/BaseButton.svelte';
	import {
		IconPlayerRecordFilled,
		IconPlayerStopFilled,
		IconRepeat,
		IconTextGrammar	} from '@tabler/icons-svelte';
	import { createEventDispatcher, onMount } from 'svelte';
	import { getCurrentDateTime } from '$lib/common/utils';
	import LoadingSpinner2 from '$components/common/LoadingSpinner2.svelte';
	import {encodeWav, loadToFfmpeg} from "$lib/common/ffmpeg-helpers";
    import {page} from '$app/stores';
	import {ffmpegState, loadFfmpeg} from '$lib/state/ffmpeg-state';

	import * as sdk from "microsoft-cognitiveservices-speech-sdk";
	import NotificationStore from '$lib/state/notification-state';
	import { NotificationType } from '$common/models/enums';

	const speechConfig = sdk.SpeechConfig.fromSubscription($page.data?.envs.AZURE_SUBSCRIPTION_KEY, $page.data?.envs.AZURE_REGION);
	speechConfig.outputFormat = sdk.OutputFormat.Detailed;
	speechConfig.setProfanity(sdk.ProfanityOption.Raw);
	speechConfig.speechRecognitionLanguage = "he-IL";

	export let audioUrl: string | null;
	export let id: string;
	export const sentence: string = "";
    export let disabled:boolean;
	export let audioRecordedButNotYetChecked = false;
	export let transcription: string;
	let mediaRecorder: MediaRecorder | null = null;
	let audioBlob: string;
	let audioBlobToSave: Blob;
	let time = 0;
	let isRunning = false;
	let isChecking = false;
	let interval: NodeJS.Timeout;
	let stream: MediaStream;

	const dispatcher = createEventDispatcher();

	onMount(async () => {
		try {
			await loadFfmpeg();
		} catch (error) {
			NotificationStore.push({
				type: NotificationType.error,
				message: 'Failed to load audio processing. Please refresh the page and try again.'
			}, 7);
			console.error('Failed to load ffmpeg:', error);
		}
		clearInterval(interval)
	});

	const startRecording = async () => {
		if (!$ffmpegState.isLoaded) {
			NotificationStore.push({
				type: NotificationType.error,
				message: 'Failed to load audio processing. Please refresh the page and try again.'
			}, 7);
			console.error('FFmpeg is not loaded yet');
			return;
		}

		try {
			stream = await navigator.mediaDevices.getUserMedia({ audio: true });
			mediaRecorder = new MediaRecorder(stream);
			mediaRecorder.addEventListener('dataavailable', async ({ data }) => {
				try {
					const audioBlobToProcess = new File([data], `${id}_${getCurrentDateTime()}.webm`, {type: `audio/webm`}) as File;
					await loadToFfmpeg(audioBlobToProcess);
					const result = await encodeWav();

					if (result) {
						audioBlobToSave = new File([result], `${id}_${getCurrentDateTime()}.wav`, {
							type: `audio/wav`,
							lastModified: Date.now()
						}) as File;

						audioBlob = URL.createObjectURL(audioBlobToSave);
					} else {
						console.error('Failed to encode audio');
					}
				} catch (error) {
					console.error('Error processing audio:', error);
				}
			});
			audioUrl = '';
			audioBlob = '';
			mediaRecorder.start();
			isRunning = true;
			interval = setInterval(() => {
				time += 1;
			}, 1000);
		} catch (error) {
			console.error('Error starting recording:', error);
		}
	};

	const stopRecording = () => {
		if (mediaRecorder && isRunning) {
			mediaRecorder.stop();

			clearInterval(interval);
			time = 0;
			isRunning = false;
			audioRecordedButNotYetChecked = true;
		}
	};

	const recognizeSpeech = async() => {
        isChecking = true;

        try {
            // Ensure we have a valid audio file
            if (!audioBlobToSave) {
                throw new Error('No audio file available');
            }

            // Create an AudioConfig from the audio file
            const audioConfig = sdk.AudioConfig.fromWavFileInput(audioBlobToSave as File);

            // Create a SpeechRecognizer
            const recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);

            // Start speech recognition
            recognizer.recognizeOnceAsync((result) => {
                if (result.reason === sdk.ResultReason.RecognizedSpeech) {
					console.log(result);
					console.log(result.text);
                    transcription = result.text;
					try {
						dispatcher('transcription', { detail: transcription });
					} catch (e) {
						console.error('Error dispatching event:', e);
					}

                } else {
					console.log(result.errorDetails);
                    transcription = `Error: ${result.errorDetails}`;
                }
                isChecking = false;
				audioRecordedButNotYetChecked = false;
            });
        } catch (error) {
            console.error("Error during speech recognition:", error);
            transcription = "Error processing audio. Please try again.";
            isChecking = false;
			audioRecordedButNotYetChecked = false;
        }

	}

	const formatTime = (timeInSeconds: number) => {
		const minutes = Math.floor(timeInSeconds / 60)
			.toString()
			.padStart(2, '0');
		const seconds = (timeInSeconds % 60).toString().padStart(2, '0');
		return `${minutes}:${seconds}`;
	};

	const onRecordingCanceled = () => {
		audioBlob = '';
		audioRecordedButNotYetChecked = false;
		dispatcher('audioCanceled');
	};
</script>

<div class="flex flex-col">
	<div class="flex items-center gap-3">
		{#if !isRunning && !audioBlob}
			<BaseButton disabled={disabled || !$ffmpegState.isLoaded || $ffmpegState.hasError} on:click={startRecording}>
				<IconPlayerRecordFilled />
			</BaseButton>
		{:else if !isRunning && audioBlob}
			<BaseButton on:click={onRecordingCanceled} disabled={isChecking}>
				<IconRepeat />
			</BaseButton>
		{:else}
			<BaseButton on:click={stopRecording}>
				<IconPlayerStopFilled />
			</BaseButton>
		{/if}

		<BaseButton
				className={audioRecordedButNotYetChecked ? "pulse-ping-animation" : ""}
				on:click={recognizeSpeech}
				disabled={!audioBlob || isChecking}
				name="check-record"
			>
			<IconTextGrammar />
		</BaseButton>
		{#if $ffmpegState.isLoading}
			<div class="flex gap-2 text-base text-info items-center">
				<LoadingSpinner2 />
				<p>Loading audio processor...</p>
			</div>
		{:else if $ffmpegState.hasError}
			<div class="flex gap-2 text-base text-error items-center">
				<p>{$ffmpegState.errorMessage}</p>
			</div>
		{:else if isRunning}
			<div class="flex gap-2 text-base text-error items-center">
				{formatTime(time)}
				<div class="w-4 h-4 bg-red-600 rounded-full animate-pulse"></div>

				<p>Recording...</p>
			</div>
		{:else if isChecking}
			<LoadingSpinner2 />
		{:else}
			<audio class="w-full max-w-full h-10 sm:h-8" controls={audioBlob!==undefined && audioBlob.length > 0} src={audioBlob}
			></audio>
		{/if}
	</div>
</div>

<style>
	audio {
		height: 2.3em;
	}
</style>
