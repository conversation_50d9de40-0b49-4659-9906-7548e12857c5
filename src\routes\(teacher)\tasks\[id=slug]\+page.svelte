<script lang="ts">
    import {type ModalSettings, getModalStore} from "@skeletonlabs/skeleton";
    import BaseButton from '$components/common/BaseButton.svelte';
    import {
        IconCopy,
        IconDeviceDesktopQuestion,
        IconDeviceDesktopShare,
        IconDeviceFloppy,
        IconPlus
    } from '@tabler/icons-svelte';
    import ManagementButtons from '$components/tasks/ManagementButtons.svelte';
    import TaskGeneralInfo from '$components/tasks/TaskGeneralInfo.svelte';
    import TaskSentenceSearch from '$components/tasks/TaskSentenceSearch.svelte';
    import AdditionalTasks from '$components/tasks/AdditionalTasks.svelte';
    import {SentenceFilterState} from '$lib/state/sentence-filter-state';
    import {onDestroy, onMount} from 'svelte';
    import {t} from '$lib/i18n/config';
    import {
        CurrentEditableState,
        initialCurrentEditable
    } from '$lib/state/task-current-editable-state';
    import {
        convertStringTimeToDate,
        formatToPickerDate,
        getTimeFromDateString,
        loadingWrap, mergeDateTime
    } from '$lib/common/utils';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
    import {mapper} from '$common/core/mapper';
    import type {TaskDto} from '$common/models/dtos/task.dto';
    import {SentenceApiClient} from '$lib/core/api-clients/sentence-api-client';
    import {slide} from 'svelte/transition';
    import ContentEditor from '$components/content/ContentEditor.svelte';
    import {initialSentencePaging, SentencePagingState} from '$lib/state/sentence-paging-state';
    import _ from 'lodash';
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import {downloadPDF} from '$lib/common/pdf-helpers';
    import {page} from '$app/stores';
    import {Routes} from '$common/core/routes';
    import NotificationState from '$lib/state/notification-state';
    import {NotificationType, TaskFilter, TaskMode, TaskSpeedMode} from '$common/models/enums';
    import {LoadingState} from '$lib/state/loading-state';
    import {fade} from 'svelte/transition';
    import {beforeNavigate, goto} from "$app/navigation";
    import {loadFfmpeg} from "$lib/state/ffmpeg-state";
    import {initialSentenceFilter} from "$common/models/filters/sentence-filter.dto";
    import {generateGuid} from "$common/core/utils";
    import {popup} from '@skeletonlabs/skeleton';
    import {browser} from "$app/environment";
    import {PreviousTaskPageState} from "$lib/state/previous-task-page.state";
    import TaskPresentationPopup from '$components/groups/TaskPresentationPopUp.svelte';
    import type {SentenceInTaskDto} from "$common/models/dtos/sentence.dto";
    import LexicalContent from "$components/common/LexicalContent.svelte";
	import { GroupHolidaysApiClient } from "$lib/core/api-clients/groupHolidays-api-client.js";
	import { GroupHolidaysExcApiClient } from "$lib/core/api-clients/groupHolidaysExc-api-client.js";

    export let data;

    let selectedGroup;

    const incentiveContent: IncentiveContentDto[] = data.incentiveContent?.data;
    let showAdditionalButton = false;
    let firstLoad = true;
    let hasEmptyFields = false;
    $: hasEmptyAudio = [TaskMode.voice, TaskMode.audiodic, TaskMode.listen].includes($CurrentEditableState.startingMode)
    	&& !$CurrentEditableState.sentences.every((s) => s.audioUrl);
    const modalStore = getModalStore();
    let prevState;
    let bypass = false;
    let isDuplicateTask;
    let mountFinished = false;
    let routeToProceedBypassedNavigation;
    let isToolTipActive = false;
    let menu = null;
    let isOpenDropDown = false;
    let handleOutsideClick;
    let handleEscape;
    let taskLang: 'EN' | 'RU';
    let taskFont = false;
    let taskSentences: SentenceInTaskDto[];
    let isTaskPresentationPopupVisible = false;

    $: groups = data.groups.data as GroupDto[];
    $: $CurrentEditableState.sentences = data.task?.sentences || [];

    $: isDisableSave =
        !$CurrentEditableState.sentences ||
        $CurrentEditableState.sentences?.length === 0 ||
        !$CurrentEditableState.groupId ||
        hasEmptyFields || hasEmptyAudio || !canBeSaved;

    $: if ($CurrentEditableState.sentences) {
        checkToDisableSaveButtonInTask();
    }


    $: canBeSaved = !_.isEqual(prevState, $CurrentEditableState) && mountFinished;


    $: notSavedConfirmationModal = {
        type: 'confirm',
        title: `<h1 dir="auto">${$t('tasks.modalLeave.title')}</h1>`,
        body: ` <p dir="auto">${$t('tasks.modalLeave.body')}</p>`,
        buttonTextCancel: $t('tasks.modalLeave.buttonTextCancel'),
        buttonTextConfirm: $t('tasks.modalLeave.buttonTextConfirm'),
        regionFooter: 'flex gap-5 border-2 border-red-900',

        response: (r: boolean) => {
            bypass = r;
            if (bypass) {
                goto(routeToProceedBypassedNavigation, {noScroll: true});
            }

        }
    };

    let composerComponent;


    onMount(async () => {

        $LoadingState = true;
        try {
            await loadFfmpeg();
        } catch (error) {
            console.error('Failed to load ffmpeg on tasks page:', error);
        }
        isDuplicateTask = $page.url.searchParams.get('duplicate');
        CurrentEditableState.set(
            isDuplicateTask
                ? {...data.task, groupId: null}
                : data.task
                    ? data.task
                    : _.cloneDeep(initialCurrentEditable));
        $CurrentEditableState.id = isDuplicateTask || !data.task?.id ? $page.params.id : data.task.id;
        $CurrentEditableState.sentencesToChoose = data.sentences.data;
        $CurrentEditableState.sentencesToChooseCount = data.sentences.count;
        $CurrentEditableState.time = data.task?.time ? getTimeFromDateString(data.task?.time) : '12:00';
        $CurrentEditableState.users = data?.users?.data;
        $CurrentEditableState.date = formatToPickerDate(
            data.task?.date ? new Date(data.task?.date) : new Date()
        );

        console.log('Current editable state:', $CurrentEditableState);

        if ($CurrentEditableState.additionalTasks?.phantom) {
            const taskSpeedMode = $CurrentEditableState.additionalTasks?.phantom?.speedMode || 'slow';
            $CurrentEditableState.additionalTasks.phantom.speedMode = TaskSpeedMode[taskSpeedMode]
        }
        $CurrentEditableState.task_incentive_content = data.task?.task_incentive_content || [];


        $CurrentEditableState.groupId = $CurrentEditableState.groupId
            ? $CurrentEditableState.groupId
            : $page.url.searchParams.get('groupId');
        $CurrentEditableState.page_content = data?.task?.pageContent ? data?.task?.pageContent : {...initialCurrentEditable.page_content};


        if (isDuplicateTask || !data.task) {
            _.values($CurrentEditableState.additionalTasks).forEach(x => x.id = generateGuid());
        }

        if ($CurrentEditableState.additionalTasks && !$CurrentEditableState.additionalTasks?.voice) {
            console.log('no voice task - generating new one');
            $CurrentEditableState.additionalTasks.voice = {
                id: generateGuid(),
                time: 0,
                delay: 0,
                maxScore: 100,
                hintEnabled: true,
                enabled: false,
                mode: TaskMode.voice,
                speedMode: TaskSpeedMode.slow,
                allowAnonymous: false,
                allowRetry: true
            }
        }

        if (browser) {
            const editor = composerComponent?.getEditor();
            editor?.registerUpdateListener(({editorState}) => {
                console.log(JSON.stringify(editorState));
            });
            handleOutsideClick = (event) => {
                if (isOpenDropDown && !menu?.contains(event.target)) {
                    isOpenDropDown = false;
                }
            };

            handleEscape = (event) => {
                if (isOpenDropDown && event.key === 'Escape') {
                    isOpenDropDown = false;
                }
            };

            document.addEventListener('click', handleOutsideClick, false);
            document.addEventListener('keyup', handleEscape, false);

        }


        if (browser) {
            document.getElementById('page').addEventListener('scroll', (e) => {
                const element = e.target as HTMLElement;
                showAdditionalButton = element.scrollTop > 158;
            });
        }


        handleGroupChange(!!$page.url.searchParams.get('groupId'));


        $CurrentEditableState.isNew = !data.task;
        prevState = _.cloneDeep($CurrentEditableState);


        mountFinished = true;
        $LoadingState = false;
    });


    const unsubscribe = SentenceFilterState.subscribe(async () => {
        if (!firstLoad) {
            SentencePagingState.set({...initialSentencePaging});
            await loadingWrap(async () => {
                const response = await new SentenceApiClient().getSentences();
                $CurrentEditableState.sentencesToChoose = response?.data ?? [];
                $CurrentEditableState.sentencesToChooseCount = response.count ?? 0;
            });
        }
        firstLoad = false;
    });

    const componentDisplayStatus = {
        additionalTask: false,
        taskContent: false
    };

    const toggleComponentDisplay = (name: 'additionalTask' | 'taskContent' | 'searchPopUp') => {
        componentDisplayStatus[name] = !componentDisplayStatus[name];
    };


    beforeNavigate((event) => {
        const {willUnload} = event;
        if (canBeSaved && !bypass && !willUnload) {
            const {to, cancel} = event;
            const {route, from, params} = to;
            cancel();
            routeToProceedBypassedNavigation = params.id ? `/groups/${params.id}` : route.id.replace(/\/\([^)]+\)/, '');
            modalStore.trigger(notSavedConfirmationModal);
        } else {
            if (browser) {
                document.getElementById('page')?.scrollTo(0, 0);
            }
            closeAdditionalTaskBeforeNavigate()
        }
    });

    $: if (canBeSaved) {
        window.onbeforeunload = !window.onbeforeunload
            ? () => {
                return 'Are u sure?';
            }
            : null;
    } else {
        window.onbeforeunload = null;
    }


    const setTimeFromSelectedGroup = (selectedGroup) => {
        if (selectedGroup) {
            const date =
                $CurrentEditableState.type === 'class'
                    ? selectedGroup?.timeStart?.toString()
                    : selectedGroup?.timeEnd?.toString();
            $CurrentEditableState.time = getTimeFromDateString(date);
        } else {
            $CurrentEditableState.time = '12:00';
        }
    };

    const copyLink = async () => {
        let currentUrl = $page.url.origin + ($CurrentEditableState.isPublic ? Routes.PublicT : Routes.T) + $page.params.id;
        try {
            await navigator.clipboard.writeText(currentUrl);
            NotificationState.push({
                type: NotificationType.success,
                message: $t('tasks.notification.link.success')
            });
        } catch (error) {
            NotificationState.push({
                type: NotificationType.error,
                message: $t('tasks.notification.link.error')
            });
        }
    };

    const back = () => {
        if (browser) {
            document.getElementById('page')?.scrollTo(0, 0);
            closeAdditionalTaskBeforeNavigate()
            goto($PreviousTaskPageState);
            // window.history.back();
        }
    };

    const checkMainModeAndSetAdditionalTask = () => {
        if ($CurrentEditableState.startingMode !== TaskMode.translation) {
            const mode = Object.keys($CurrentEditableState.additionalTasks).find((t) =>  
                t === $CurrentEditableState.startingMode
            )
            console.log('Mode to enable', mode);
            try {
                if (mode !== undefined) {
                    $CurrentEditableState.additionalTasks[mode].enabled = true;
                }
                } catch (e) {
                    console.error('Error enabling additional task according to main mode:', e);
            }
        }
    }

    const save = async () => {
        checkMainModeAndSetAdditionalTask();
        const task = _.omit($CurrentEditableState, [
            'sentencesToChoose',
            'sentencesToChooseCount',
            'incentiveContent',
            'isNew',
            'isPublic',
            'users',
        ]);

        const dto = mapper<TaskDto, unknown>({
            ...task,
            date: new Date(task.date.toString() || ''),
            time: new Date(convertStringTimeToDate(task.time.toString() || '')),
            dateTime: new Date(mergeDateTime(new Date(task.date.toString() || '').toString(), new Date(convertStringTimeToDate(task.time.toString() || '')).toString()))
        });

        const res = await loadingWrap(() => new TaskApiClient().createTask(dto));

        if (res) {
            $CurrentEditableState.isNew = false;
            prevState = _.cloneDeep($CurrentEditableState)
            NotificationState.push({
                type: NotificationType.success,
                message: $t('tasks.notification.save.success')
            });
            if (isDuplicateTask || !data.task) {
                bypass = true;
                goto(Routes.Tasks + $CurrentEditableState.id, {invalidateAll: true}).then(() => {
                    prevState = _.cloneDeep($CurrentEditableState)
                })
            }
        } else {
            NotificationState.push({
                type: NotificationType.error,
                message: $t('tasks.notification.save.error')
            });
        }
    };

    const deleteTask = async () => {
        const modalResult = await new Promise<boolean>((resolve) => {
            const modal: ModalSettings = {
                type: 'confirm',
                title: `<h1 dir="auto">${$t('tasks.modalTaskDelete.title')}</h1>`,
                body: `<p dir="auto">${$t('tasks.modalTaskDelete.body')}</p>`,
                buttonTextConfirm: $t('tasks.modalTaskDelete.buttonTextConfirm'),
                buttonTextCancel: $t('tasks.modalTaskDelete.buttonTextCancel'),
                response: (r: boolean) => resolve(r)
            };
            modalStore.trigger(modal);
        });
        if (modalResult) {
            const result = await new TaskApiClient().deleteTask($CurrentEditableState.id);
            if (result) {
                NotificationState.push({
                    type: NotificationType.success,
                    message: $t('tasks.taskNotification.success')
                });
                back()
            } else {
                NotificationState.push({
                    type: NotificationType.error,
                    message: $t('tasks.taskNotification.error')
                });
            }
        } else {
            NotificationState.push({
                type: NotificationType.success,
                message: $t('tasks.taskNotification.cancel')
            })
        }
    }

    const contentUpdated = (e, type: 'lexical' | 'content') => {
        if (type === 'content') {
            $CurrentEditableState.content = e.detail.content;

        }
        NotificationState.push({
            type: NotificationType.success,
            message: $t('tasks.contentNotification.success')
        });

    }

    const downloadPdfFile = () => {
        downloadPDF($CurrentEditableState, $t);
    };

    const handleGroupChange = async (setTime = true) => {
        const foundGroup = groups?.find((x) => x.id === $CurrentEditableState.groupId);
        if (!foundGroup) return;
        selectedGroup = foundGroup
        selectedGroup.groupHoliday = await new GroupHolidaysApiClient().getGroupHolidaysByGroupId(selectedGroup.id!);
        selectedGroup.groupHolidaysExceptions = await new GroupHolidaysExcApiClient().getGroupHolidaysExceptionsByGroupId(selectedGroup.id!);
        if (setTime) setTimeFromSelectedGroup(selectedGroup);
        $CurrentEditableState.lang = selectedGroup?.lang;
        $CurrentEditableState.isPublic = selectedGroup?.isPublic;
        $CurrentEditableState.groupId = selectedGroup?.id;
        $CurrentEditableState.type = $CurrentEditableState.isPublic ? 'sandbox' : prevState?.type || $CurrentEditableState.type;
        checkToDisableSaveButtonInTask();
    };

    const checkToDisableSaveButtonInTask = () => {
        const {sentences} = $CurrentEditableState;
        const emptyFieldInValues = !sentences?.every((s) => s?.value);
        const emptyFieldInTranslations = !sentences?.every((s) => {
            const findTranslations = s?.translations?.find((t) => t.lang === $CurrentEditableState.lang);
            return findTranslations && findTranslations?.value;
        });

        const mainModeRequiresAudio = [TaskMode.voice, TaskMode.audiodic, TaskMode.listen].includes($CurrentEditableState.startingMode);
        hasEmptyAudio = mainModeRequiresAudio && !sentences.every((s) => s.audioUrl);

        isToolTipActive = emptyFieldInTranslations || hasEmptyAudio;
        hasEmptyFields = emptyFieldInValues || emptyFieldInTranslations;
    };

    const closeAdditionalTaskBeforeNavigate = () => {
        if (componentDisplayStatus.additionalTask) {
            toggleComponentDisplay('additionalTask')
        }
    }

    function createToolTipHover(index) {
        return {
            event: 'hover',
            target: `toolTipHover-${index}`,
            placement: 'bottom'
        };
    }


    const copyLinkToClipBoard = async () => {
        const pathToTask = $CurrentEditableState.type === TaskFilter.sandbox ? '/pt/' : '/t/';
        const rootPath: string = $page.url.origin || '';
        const link = `${rootPath}${pathToTask}${$page?.params?.id}`;
        await navigator.clipboard.writeText(link);
        isOpenDropDown = false;
    };

    const duplicateTask = async () => {
        const rootPath: string = $page.url.origin || '';
        const link = `${rootPath}/tasks/${generateGuid()}?isn=true&duplicate=${$page?.params?.id}`;
        await goto(link);
    };

    const openPopUp = (random: boolean) => {
        taskFont = $CurrentEditableState.hebrewFont;
        taskLang = $CurrentEditableState.lang;
        taskSentences = random ? _.shuffle($CurrentEditableState.sentences) : $CurrentEditableState.sentences;
        toggleTaskPresentationPopupVisible();
    };

    const toggleTaskPresentationPopupVisible = () => {
        isTaskPresentationPopupVisible = !isTaskPresentationPopupVisible;
    };

    onDestroy(() => {
        unsubscribe();
        window.onbeforeunload = null;
        document.removeEventListener('click', handleOutsideClick, false);
        document.removeEventListener('keyup', handleEscape, false);
        $SentencePagingState = _.cloneDeep(initialSentencePaging)
        $SentenceFilterState = _.cloneDeep(initialSentenceFilter);
        $CurrentEditableState = _.cloneDeep(initialCurrentEditable);
    });

</script>

{#if mountFinished}

    {#if isTaskPresentationPopupVisible}
        <div class=" fixed top-0 left-0 h-[100vh] w-[100vw] z-20">
            <TaskPresentationPopup {toggleTaskPresentationPopupVisible} {taskFont} {taskSentences} {taskLang}/>
        </div>
    {/if}

    {#if showAdditionalButton && !$LoadingState}
        <div
                use:popup={createToolTipHover(0)}
                transition:fade={{ delay: 0, duration: 150 }}
                class="fixed z-[60] top-[15px] left-[370px] w-[146px] h-[45px]"
        >
            {#if isToolTipActive}
                <div class="card p-4 variant-filled-secondary z-[999]" data-popup="toolTipHover-0">
                    <p>
                        {hasEmptyFields ? $t('tasks.managementButtons.saveHover') : hasEmptyAudio 
                            ? $t('tasks.managementButtons.saveAudioHover') : ''}
                    </p>
                    <div class="arrow variant-filled-secondary"/>
                </div>
            {/if}
            <BaseButton disabled={isDisableSave} className="w-full h-full" on:click={save}>
                {$t('tasks.managementButtons.save')}
                <IconDeviceFloppy/>
            </BaseButton>
        </div>
    {/if}

    <div class="p-8 flex flex-col {$LoadingState ? 'animate-out fill-mode-backwards' : ''}">
        <div class="flex flex-col gap-5 mt-10">
            <div class="card mt-6 p-5 w-full text-token flex justify-between variant-glass-primary z-10">
                <TaskGeneralInfo
                        groups={data?.groups.data}
                        on:groupChanged={handleGroupChange}
                        on:taskTypeChanged={(e)=>{
                            setTimeFromSelectedGroup(e.detail)
                        }}
                        on:taskModeChanged={() => {
                            checkToDisableSaveButtonInTask();
                        }}
                />
                {#key isToolTipActive}
                    <ManagementButtons
                            task={data.task}
                            on:link={copyLink}
                            on:save={save}
                            on:back={back}
                            on:download={downloadPdfFile}
                            isDisableSave={isDisableSave}
                            isToolTipActive={isToolTipActive}
                            toolTipText={hasEmptyFields ? $t('tasks.managementButtons.saveHover') : hasEmptyAudio 
                            ? $t('tasks.managementButtons.saveAudioHover') : ''}
                    />
                {/key}
            </div>
        </div>

        {#if $CurrentEditableState.groupId}
            <div class="flex justify-between mt-10">
                <div class="flex flex-col gap-5  w-48">
                    <BaseButton on:click={() => toggleComponentDisplay('taskContent')} name="content">
                        {$t('tasks.content')}
                        <IconPlus/>
                    </BaseButton>
                </div>
                {#if !$page.url.searchParams.get('isn')}
                    <div bind:this={menu} dir="ltr" class="relative w-fit flex gap-2">
                        <BaseButton
                                className="bg-secondary text-black border-[1px] border-accent"
                                disabled={!$CurrentEditableState.sentences.length > 0}
                                on:click={() => openPopUp(true)}>
                            <IconDeviceDesktopQuestion size={20} stroke="1.5"/>
                        </BaseButton>
                        <BaseButton
                                disabled={!$CurrentEditableState.sentences.length > 0}
                                on:click={() => openPopUp(false)}>
                            <IconDeviceDesktopShare size={20} stroke="1.5"/>
                        </BaseButton>
                        <button
                                type="button"
                                class="flex items-center variant-filled h-full"
                                id="menu-button"
                                aria-expanded="true"
                                aria-haspopup="true"
                        >
                            <BaseButton class="flex-1 p-0 pl-0 w-fit" on:click={copyLinkToClipBoard}>
                                <IconCopy size={20} stroke="1.5"/>
                            </BaseButton>
                            <div class="flex-1 p-1 pr-2" on:click={() => (isOpenDropDown = !isOpenDropDown)}>
                                <svg
                                        class="-mr-1 h-5 w-5 text-gray-400"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        aria-hidden="true"
                                >
                                    <path
                                            fill-rule="evenodd"
                                            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                            clip-rule="evenodd"
                                    />
                                </svg>
                            </div>
                        </button>
                        {#if isOpenDropDown}
                            <div
                                    class="fade-in absolute bg-surface-50-900-token right-0 bottom-10 mt-2 w-36 z-50 list-outside origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                    role="menu"
                                    aria-orientation="vertical"
                                    aria-labelledby="menu-button"
                                    tabindex="-1"
                            >
                                <div dir="rtl" role="none">
        						<span
                                        on:click={copyLinkToClipBoard}
                                        class="text-surface-900-50-token text-start block px-4 py-2 text-sm cursor-pointer hover:bg-primary-hover-token"
                                        role="menuitem"
                                        tabindex="-1"
                                        id="menu-item-0"
                                >
        							{$t('students.students.table.copyButton')}
        						</span>
                                    <hr/>
                                    <span
                                            on:click={duplicateTask}
                                            class="text-surface-900-50-token text-start block px-4 py-2 text-sm cursor-pointer hover:bg-primary-hover-token"
                                            role="menuitem"
                                            tabindex="-1"
                                            id="menu-item-1"
                                    >
        							{$t('students.students.table.duplicateButton')}
        						</span>
                                </div>
                            </div>
                        {/if}


                    </div>
                {/if}
            </div>

            {#if componentDisplayStatus.taskContent}
                <div
                        dir="auto"
                        class="mt-10 w-full card variant-glass-primary content-wrapper"
                        transition:slide={{ duration: 500 }}
                >
                    {#if $CurrentEditableState.content}
                        <ContentEditor
                                readMode={false}
                                id={$CurrentEditableState.id}
                                bind:content={$CurrentEditableState.content}
                                on:contentUpdated={(e)=>contentUpdated(e,'content')}
                        />
                    {:else}
                        <div class=" pt-5 pb-10 pl-9 pr-9">
                            <LexicalContent
                                    isReadMode={false}
                                    on:contentUpdated={(e)=>contentUpdated(e,'lexical')}
                                    bind:content={$CurrentEditableState.page_content}
                            />
                        </div>

                    {/if}
                </div>
            {/if}

            <TaskSentenceSearch {checkToDisableSaveButtonInTask} groupLevel={selectedGroup?.level}/>

            <div class="flex justify-between mt-10">
                <BaseButton on:click={() => toggleComponentDisplay('additionalTask')}>
                    {$t('tasks.settings')}
                </BaseButton>
                <BaseButton disabled={$CurrentEditableState.isNew} on:click={deleteTask}
                            className="bg-red-700">{$t('tasks.delete')}</BaseButton>
            </div>
            {#if componentDisplayStatus.additionalTask}
                <AdditionalTasks {incentiveContent}/>
            {/if}
        {/if}
    </div>

{/if}

<style>
    .content-wrapper {
        position: relative;
        z-index: 2;
    }
</style>