<script>
    import BaseInput from '../common/BaseInput.svelte';
    import {t} from '$lib/i18n/config';
    import _ from 'lodash';
    import {get} from 'svelte/store';
    import {StudentsRequestsFilterState} from "$lib/state/students-requests-filter.state";

    let inputValue = '';

    const onInput = _.debounce(
        () => StudentsRequestsFilterState.set({...get(StudentsRequestsFilterState), search: inputValue}),
        1000
    );
</script>

<div class="flex flex-col min-h-[80px]">
    <div class="flex flex-row gap-10 items-center w-full justify-between">
        <div class="flex gap-x-5">
            <div class="flex w-72">
                <BaseInput
                        name="search"
                        placeHolder={$t('students.students.filters.placeHolderSearch')}
                        on:input={onInput}
                        bind:value={inputValue}
                        title={$t('students.students.filters.search')}
                />
            </div>
        </div>
    </div>
</div>
