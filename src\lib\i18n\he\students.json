{"tabs": {"requests": "בקשת הצטרפות", "students": "תלמידים"}, "requests": {"table": {"head": {"tz": "תעודת זהות", "fullname": "שם מלא", "email": "דוא''ל", "whatsapp": "WhatsApp", "groupStartDate": "תאריך פתיחת כיתה", "learnStartDate": "תאריך תחילת לימודים", "lang": "שפה", "level": "רמה", "action": "טיפול", "application": "תאריך בקשה"}, "handleButton": "הג<PERSON>ר", "deleteButton": "מחק"}, "modal": {"previousmodalname": "רישום סטודנט", "incompletegroup": "הסטודנט רשום בכיתה שטרם סיימה, יש לקבוע תאריך סיום הכיתה", "studentTitle": "פרטים שהוזנו ע''י הסטודנט", "groupTitle": "שיבוץ סטודנט חדש בכיתה", "groupSubtitle": "שיבוץ סטודנט חדש בכיתה", "levelSubtitle": "הר<PERSON>ה שנבחרה היא", "langSubtitle": "ושפת האם היא", "groupStartSubtitle": "תאריך תחילת הלימוד ע''פ הסטודנט הוא", "submit": "שמירה", "cancel": "ביטול", "group": "כיתה", "dob": "תאריך לידה", "groupStart": "תחילת לימודים", "tasksStart": ":תרגילים החל מ "}, "modalDelete": {"title": "?בטוח/ה", "body": "בקשת הסטודנט תימחק", "buttonTextCancel": "ביטול", "buttonTextConfirm": "מחק", "notification": {"success": "בקשת הסטודנט נמחקה בהצלחה", "cancel": "בקשת הסטודנט לא נמחקה", "error": "...משהו השתבש"}}, "notifications": {"handle": {"success": "Студент был успешно добавлен"}}}, "students": {"filters": {"search": "חיפוש", "isActive": "פעילים בלבד", "info": "מידע מלא", "placeHolderSearch": "חיפוש"}, "table": {"head": {"tz": "תעודת זהות", "fullname": "שם מלא", "email": "דוא''ל", "whatsapp": "WhatsApp", "group": "כיתה", "action": "טיפול", "regDate": "תאריך רישום", "startInGroupDate": "תאריך תחילת לימודים", "comment": "הערה", "lastTaskResult": "<PERSON><PERSON><PERSON><PERSON> אחרון", "averageTaskResult": "ציון ממוצע", "lastTaskDelay": "<PERSON><PERSON><PERSON> ביצוע של תרגיל אחרון", "averageTaskDelay": "<PERSON><PERSON><PERSON> ביצוע תרגיל ממוצע", "statistics": "Статистика"}, "hours": "<PERSON>а<PERSON>ы", "days": "<PERSON><PERSON>и", "editButton": "עריכה", "copyButton": "העת<PERSON> קישור", "duplicateButton": "צור עותק"}, "modal": {"previousmodalname": "עריכת פרטי סטודנט", "title": "עריכת פרטי סטודנט", "submit": "שמירה", "cancel": "ביטול", "statistics": "Статистика", "totalHours": "Количество часов обучения", "totalDays": "Количество дней обучения", "fields": {"lastname": "שם משפחה", "firstname": "שם פרטי", "dob": "תאריך לידה", "phone": "טלפון", "email": "דוא''ל", "whatsapp": "WhatsApp", "comment": "הערה", "tz": "תעודת זהות", "photo": "Фото", "teudatOle": "Теудат Оле"}, "pastDataDescription": "*-במידה והסטודנט השתתף בקורס שלנו בעבר, פרטיו מסומנים כ", "formFieldsErrors": {"tz": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> להכיל 6 עד 10 תווים", "firstname": "שם פרטי צריך לכלול שני תוים לפחות", "lastname": "שם משפחה צריך לכלול שני תוים לפחות", "email": "ודא שדוא''ל תקין", "phone": "ודא שמס<PERSON>ר טלפון תקין", "whatsapp": "<PERSON><PERSON><PERSON><PERSON> WhatsApp ודא שמספר ", "dob": "חובה למלא תאריך"}, "groupsHistory": {"switch": "העבר לכיתה אחרת", "stop": "הפסק", "table": {"totalDays": "Кол-во дней", "totalHours": "Кол-во часов", "endlearning": "סיום לימודים", "starttasks": ":תרגילים החל מ", "startlearning": "תחילת לימודים", "group": "כיתה"}}, "updateStudentGroupModal": {"edit": "עריכה", "group": "כיתות", "fields": {"group": "כיתה", "datestart": "תאריך התחלה", "taskstart": ":תרגילים החל מ", "dateend": "תאריך סיום"}}, "switchStudentGroupModal": {"breadcrumb": "העבר לכיתה אחרת", "fromgroup": "רשום בכיתה", "fields": {"dateend": "תאריך סיום", "tonewgroup": ":הע<PERSON>ר לכיתה", "datestart": "תאריך התחלה", "taskstart": ":תרגילים החל מ"}}, "stopStudentGroupModal": {"stop": "הפסק", "group": "כיתה", "title": "?מהו תאריך הסיום", "fields": {"dateend": "תאריך סיום"}}, "deleteStudentGroupModal": {"delete": "Удалить", "group": "כיתה", "title": "בטוח/ה", "buttons": {"save": "כן", "cancel": "לא"}}, "editWarning": {"title": "?בטוח/ה", "body": "הפעולה תכניס שינויים להסטוריה של תהתלמיד", "cancel": "ביטול", "confirm": "אשר"}}, "notifications": {"edit": {"success": "Студент успешно обновлен"}, "updateStudentGroup": {"success": "Группа студента была успешно обновлена"}, "deleteStudentGroup": {"success": "Группа студента была успешно удалена"}, "stopStudentGroup": {"success": "Группа студента была успешно остановлена"}, "switchStudentGroup": {"success": "Группа студента была успешно изменена"}}}, "modalUsers": {"titles": {"titleToCreate": "צור משת<PERSON>ש חדש", "titleToUpdate": "עריכת פרטי המשתמש"}}, "buttons": {"save": "שמירה", "cancel": "ביטול"}, "messageerror": "הסטוד<PERSON>ט כבר לומד בתאריכים האלה "}