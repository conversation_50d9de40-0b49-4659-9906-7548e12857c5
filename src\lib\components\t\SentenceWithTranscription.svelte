<script lang="ts">
    import BaseInput from '$components/common/BaseInput.svelte';
    import {IconPlayerPlayFilled, IconMicrophone, IconPlayerPauseFilled, IconAlertTriangle} from '@tabler/icons-svelte';
    import {getModalStore, popup} from '@skeletonlabs/skeleton';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher, onMount, onDestroy} from 'svelte';
    import type {TranslationCompletion} from '$common/models/dtos/task.dto';
    import He from '$components/common/He.svelte';
    import {ComplaintModalState} from "$lib/state/complaint-modal-state";
    import {page} from "$app/stores";
    import {t} from "$lib/i18n/config";
    import {Constants} from "$api/core/constants";
    import AudioForStudents from '$components/sentences/AudioForStudents.svelte';
    import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

    const dispatcher = createEventDispatcher();

    export let sentence: TranslationCompletion;
    export let index: number;
    export let revealMode = false;
    export let disabled = false;
    
    let audio: HTMLAudioElement;
    let recognizer: SpeechSDK.SpeechRecognizer;
    let isRecording = false;
    let transcribedText = '';
    let audioPlayer;
    let currentTime = 0;
    let duration = 0;
    let playbackRate = 1;
    let isPlaying = false;
    
    const modalStore = getModalStore();
    const pageInfo = $page;

    // Azure Speech SDK configuration
    const initSpeechRecognizer = async () => {
        try {
            const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
                pageInfo.data.envs.VITE_AZURE_SPEECH_KEY,
                pageInfo.data.envs.VITE_AZURE_SPEECH_REGION
            );
            
            // Set the recognition language to Hebrew
            speechConfig.speechRecognitionLanguage = "he-IL";
            
            const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
            recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);
            
            recognizer.recognized = (s, e) => {
                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
                    transcribedText = e.result.text;
                    sentence.answerValue = transcribedText;
                }
            };
            
            recognizer.recognizing = (s, e) => {
                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {
                    transcribedText = e.result.text;
                    sentence.answerValue = transcribedText;
                }
            };
        } catch (error) {
            console.error("Error initializing speech recognizer:", error);
        }
    };

    const startRecording = async () => {
        if (!recognizer) {
            await initSpeechRecognizer();
        }
        
        isRecording = true;
        transcribedText = '';
        
        try {
            recognizer.startContinuousRecognitionAsync();
            dispatcher('inputStarted');
        } catch (error) {
            console.error("Error starting speech recognition:", error);
            isRecording = false;
        }
    };

    const stopRecording = () => {
        if (recognizer && isRecording) {
            recognizer.stopContinuousRecognitionAsync();
            isRecording = false;
        }
    };

    const handlePlayback = () => {
        if (isPlaying) {
            audioPlayer.pause();
            isPlaying = false;
        } else {
            audioPlayer.play();
            isPlaying = true;
        }
    };

    const setPlaybackSpeed = (speed) => {
        playbackRate = speed;
        if (audioPlayer) {
            audioPlayer.playbackRate = speed;
        }
    };

    const updateProgress = () => {
        if (audioPlayer) {
            currentTime = audioPlayer.currentTime;
            duration = audioPlayer.duration || 0;
        }
    };

    const handleTimeUpdate = () => {
        updateProgress();
    };

    const handleAudioEnded = () => {
        isPlaying = false;
        currentTime = 0;
    };

    const handleSliderChange = (event) => {
        const newTime = event.target.value;
        if (audioPlayer) {
            audioPlayer.currentTime = newTime;
            currentTime = newTime;
        }
    };

    const composeAudioToPlay = (fileName) =>
        fileName
            ? `https://${pageInfo.data.envs.VITE_S3_BUCKET}.s3.${pageInfo.data.envs.VITE_S3_REGION}.amazonaws.com/${pageInfo.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
            : undefined;

    const openModalForComplaint = () => {
        modalStore.trigger({type: 'component', component: 'complaintSentenceModal'});
        $ComplaintModalState.sentenceId = sentence.sentenceId;
        $ComplaintModalState.createdBy = `${pageInfo?.data?.user?.firstname} ${pageInfo?.data?.user?.lastname}`;
        $ComplaintModalState.taskId = pageInfo?.params?.id;
    };

    onMount(() => {
        if (typeof window !== 'undefined') {
            // Load the Azure Speech SDK script dynamically if not already loaded
            if (!window.SpeechSDK && !document.getElementById('azure-speech-sdk')) {
                const script = document.createElement('script');
                script.id = 'azure-speech-sdk';
                script.src = 'https://aka.ms/csspeech/jsbrowserpackageraw';
                script.async = true;
                script.onload = initSpeechRecognizer;
                document.body.appendChild(script);
            } else {
                initSpeechRecognizer();
            }
        }
    });

    onDestroy(() => {
        if (recognizer) {
            recognizer.close();
        }
    });
</script>

<div class="w-full flex flex-col gap-2 mb-5">
    <div dir="ltr" class="w-full break-all flex items-start gap-1">
        <p class="ml-1 mr-3">{index}.</p>
        <div class="flex-1 bg-gray-100 p-4 rounded">
            <p class="font-semibold mb-2">{sentence.taskValue}</p>
            
            <!-- Audio Player Controls -->
            <div class="flex flex-col w-full">
                <audio 
                    bind:this={audioPlayer}
                    src={composeAudioToPlay(sentence.audioUrl)}
                    on:timeupdate={handleTimeUpdate}
                    on:ended={handleAudioEnded}
                    bind:currentTime
                    bind:duration
                    bind:playbackRate
                    class="hidden"
                ></audio>
                
                <!-- Progress Bar -->
                <div class="flex items-center mb-2">
                    <span class="text-xs mr-2">{Math.floor(currentTime / 60)}:{Math.floor(currentTime % 60).toString().padStart(2, '0')}</span>
                    <input 
                        type="range" 
                        min="0" 
                        max={duration || 100}
                        value={currentTime}
                        on:input={handleSliderChange}
                        class="flex-1 h-1 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                    />
                    <span class="text-xs ml-2">{Math.floor(duration / 60)}:{Math.floor(duration % 60).toString().padStart(2, '0')}</span>
                </div>
                
                <!-- Controls -->
                <div class="flex justify-between items-center">
                    <div>
                        <BaseButton size="sm" on:click={handlePlayback} className="h-8 w-8 p-0 flex items-center justify-center">
                            {#if isPlaying}
                                <IconPlayerPauseFilled size="20"/>
                            {:else}
                                <IconPlayerPlayFilled size="20"/>
                            {/if}
                        </BaseButton>
                    </div>
                    
                    <div class="flex gap-2">
                        <button 
                            class="px-2 py-1 rounded text-xs {playbackRate === 0.5 ? 'bg-primary-500 text-white' : 'bg-gray-200'}"
                            on:click={() => setPlaybackSpeed(0.5)}
                        >
                            ×0.5
                        </button>
                        <button 
                            class="px-2 py-1 rounded text-xs {playbackRate === 1 ? 'bg-primary-500 text-white' : 'bg-gray-200'}"
                            on:click={() => setPlaybackSpeed(1)}
                        >
                            ×1
                        </button>
                        <button 
                            class="px-2 py-1 rounded text-xs {playbackRate === 1.5 ? 'bg-primary-500 text-white' : 'bg-gray-200'}"
                            on:click={() => setPlaybackSpeed(1.5)}
                        >
                            ×1.5
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="relative mt-4">
        {#if revealMode && sentence.mistakes.length > 0}
            <div class="top-[-25px] right-0 text-green-700 break-words" dir="auto">
                <He>{sentence.correctValue}</He>
            </div>
        {/if}
        
        <He>
            {#if !revealMode}
                <div class="flex flex-col">
                    <div class="border-2 p-2 rounded mb-2 min-h-[40px] bg-gray-50">
                        {#if transcribedText}
                            <p>{transcribedText}</p>
                        {:else}
                            <p class="text-gray-400">[{$t('t.recognizedSentence')}]</p>
                        {/if}
                    </div>
                    
                    <div class="flex">
                        <BaseButton 
                            on:click={isRecording ? stopRecording : startRecording}
                            className={isRecording ? "bg-red-500" : ""}
                        >
                            <IconMicrophone size="20"/>
                            {isRecording ? $t('t.stopRecording') : $t('t.startRecording')}
                        </BaseButton>
                    </div>
                </div>
            {:else}
                <div>
                    <p class="border-[2px] p-1 {sentence.mistakes.length === 0 ? 'border-green-700' : 'border-red-700'}">
                        {sentence.answerValue}
                    </p>
                </div>
                <div class="flex justify-between">
                    <div class="text-red-700 break-all" dir="auto">{sentence.mistakes}</div>
                    {#if pageInfo?.data?.user?.role === Constants.StudentRole}
                        <div>
                            <BaseButton on:click={openModalForComplaint} size="sm"
                                        className="h-5 bg-transparent dark:!bg-transparent text-sm font-mono text-warning-600-300-token align-middle">
                                {$t('sentences.inTask.complaint')}
                                <IconAlertTriangle size="14"/>
                            </BaseButton>
                        </div>
                    {/if}
                </div>
            {/if}
            
            <div class="pt-4">
                <AudioForStudents
                    id={sentence.sentenceId}
                    sentence={sentence.correctValue}
                />
            </div>
        </He>
    </div>
</div>
