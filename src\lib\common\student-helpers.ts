import type { ShortStudentRequestDto } from '$common/models/dtos/student-request.dto';

export const getStudentRequestObjectFromMapEn = (answerMap: Map<unknown, unknown>) => {
	return {
		id: '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		firstname: answerMap.get('6002fe42ae71172059d4d9cb')?.fn?.f || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		lastname: answerMap.get('6002fe42ae71172059d4d9cb')?.fn?.l || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		email: answerMap.get('6002fe4fae71172059d4d9d0')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		tz: answerMap.get('62f3ff4755fa496bd9fec47e')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		dob: answerMap.get('62f4011d55fa496bd9fec4bf')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		whatsapp: answerMap.get('6002fe5dae71172059d4d9d5')?.p?.p || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		phone: answerMap.get('6002fe5dae71172059d4d9d5')?.p?.p || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupStartDate: answerMap.get('638781e3dd41df99a049c912')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		learnStartDate: answerMap.get('62f4010b55fa496bd9fec4bc')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupLevel: answerMap.get('6002fe7bae71172059d4d9e7')?.c?.[0]?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupLang: answerMap.get('63779a167ad79bc03bdf51f4')?.c?.[0]?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		city: answerMap.get('649ae2b218e44b5fb8b06cd4')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		teudatOleUrl: answerMap.get('649ae2e118e44b5fb8b06e6f')?.more?.urls?.[0] || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		photoUrl: answerMap.get('649bed495c33827fe18b2038')?.more?.urls?.[0] || '',
		isHandled: false
	} as ShortStudentRequestDto;
};

export const getStudentRequestObjectFromMapRu = (answerMap: Map<unknown, unknown>) => {
	return {
		id: '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		firstname: answerMap.get('6002fe42ae71172059d4d9cb')?.fn?.f || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		lastname: answerMap.get('6002fe42ae71172059d4d9cb')?.fn?.l || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		email: answerMap.get('6002fe4fae71172059d4d9d0')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		tz: answerMap.get('62f3ff4755fa496bd9fec47e')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		dob: answerMap.get('62f4011d55fa496bd9fec4bf')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		whatsapp: answerMap.get('6002fe5dae71172059d4d9d5')?.p?.p || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		phone: answerMap.get('6002fe5dae71172059d4d9d5')?.p?.p || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupStartDate: answerMap.get('6387810fdd41df99a049c90b')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		learnStartDate: answerMap.get('62f4010b55fa496bd9fec4bc')?.d || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupLang: answerMap.get('637794d87ad79bc03bdf51a2')?.c?.[0]?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		groupLevel: answerMap.get('6002fe7bae71172059d4d9e7')?.c?.[0]?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		city: answerMap.get('649ae17c18e44b5fb8b0678b')?.t || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		teudatOleUrl: answerMap.get('649ae1c118e44b5fb8b0693a')?.more?.urls?.[0] || '',
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		//@ts-ignore
		photoUrl: answerMap.get('649becb75c33827fe18b1eae')?.more?.urls?.[0] || '',
		isHandled: false
	} as ShortStudentRequestDto;
};
