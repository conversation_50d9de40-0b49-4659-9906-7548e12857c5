<script lang="ts">
	import {
		IconLanguage,
		IconVenus,
		IconAlertTriangle
	} from '@tabler/icons-svelte';
	import { getModalStore, popup } from '@skeletonlabs/skeleton';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { createEventDispatcher } from 'svelte';
	import type { TranslationCompletion } from '$common/models/dtos/task.dto';
	import He from '$components/common/He.svelte';
	import { ComplaintModalState } from '$lib/state/complaint-modal-state';
	import { page } from '$app/stores';
	import { t } from '$lib/i18n/config';
	import { Constants } from '$api/core/constants';
	import AudioForStudents from '$components/sentences/AudioForStudents.svelte';
	import ListenBar from './task-elements/ListenBar.svelte';
	import { compareFn } from '$common/core/utils';

	const dispatcher = createEventDispatcher();

	export let hintEnabled = true;
	export let sentence: TranslationCompletion;
	export let index: number;
	export let revealMode = false;
	let transcribed = false;
	const modalStore = getModalStore();
	const pageInfo = $page;

	function createPopUpSettings(index) {
		return {
			event: 'click',
			target: `item-${index}`,
			placement: 'right'
		};
	}

	const openModalForComplaint = () => {
		modalStore.trigger({ type: 'component', component: 'complaintSentenceModal' });
		$ComplaintModalState.sentenceId = sentence.sentenceId;
		$ComplaintModalState.createdBy = `${pageInfo?.data?.user?.firstname} ${pageInfo?.data?.user?.lastname}`;
		$ComplaintModalState.taskId = pageInfo?.params?.id;
	};
</script>

<div class="w-full flex flex-col gap-2 mb-5">
	<div dir="ltr" class="w-full break-all flex items-start gap-1">
		<button
			class="border-2 border-primary-900-50-token rounded"
			use:popup={createPopUpSettings(index)}
			on:click={() => {
				if (!revealMode) sentence.hintUsedTimes++;
			}}
			disabled={!hintEnabled}
		>
			<IconLanguage />
		</button>
			<div
				class="px-5 py-2 bg-white border-black border-[1px] rounded font-semibold text-black z-50 tracking-wider"
				data-popup="item-{index}"
				onselectstart="return {$page.data.isDev}"
			>
				<He>{sentence.correctValue}</He>
				<div class="arrow bg-white border-black border-[1px]" />
			</div>
			<div
				class="justify-between flex break-normal w-full text-base font-semibold {sentence.sex ===
				'f'
					? 'text-error-600-300-token'
					: ''}"
			>
				<div class="flex ml-1" onselectstart="return {$page.data.isDev}">
					<p>{`${index}.`}</p>
					<p class="mx-1">{sentence.taskValue}</p>
				</div>
				{#if sentence.sex === 'f'}
					<div>
						<IconVenus size="30" />
					</div>
				{/if}
			</div>
	</div>
<div class="border rounded">
        <div class="flex gap-2 mt-1">
		    <ListenBar {sentence} />
        </div>
</div>
	<div class="relative">
		{#if revealMode && sentence.mistakes.length > 0}
			<div class="top-[-25px] right-0 text-green-700 break-words" dir="auto">
				<He>{sentence.correctValue}</He>
			</div>
		{/if}
		<He>
				<div>
					<p
						class="w-full h-auto border-[2px] p-1 min-h-10
							{sentence.answerValue.length === 0 ? 'border-gray-200' :
								(+sentence.mistakes.length === 0 
							? 'border-green-700'
							: 'border-red-700')}"
						placeholder="Your transcribed answer will be here"
					>
						{sentence.answerValue}
					</p>
				</div>
				<div class="flex justify-between">
					{#if sentence.mistakes.length > 0}
					{#if revealMode}
						<div class="text-red-700 break-all" dir="auto">
							{sentence.mistakes}
						</div>
					{:else}
						<div class="text-red-700 break-all" dir="auto">{$t("sentences.inTask.tryAgain") + ": " + sentence.mistakes}</div>
					{/if}
					{/if}
					{#if sentence.isCorrect && !revealMode}
						<div class="text-green-700 break-all" dir="auto">
							{$t('sentences.inTask.correct')}
						</div>
					{/if}
					{#if pageInfo?.data?.user?.role === Constants.StudentRole && revealMode}
						<div>
							<BaseButton
								on:click={openModalForComplaint}
								size="sm"
								className="h-5 bg-transparent dark:!bg-transparent text-sm font-mono text-warning-600-300-token align-middle"
							>
								{$t('sentences.inTask.complaint')}
								<IconAlertTriangle size="14" />
							</BaseButton>
						</div>
					{/if}
				</div>
            {#if !revealMode}
			<div class="pt-4">
				<AudioForStudents id={sentence.sentenceId} sentence={sentence.correctValue} 
                    on:transcription={e => {
                        sentence.answerValue = e.detail.detail;
						sentence.mistakes = compareFn(sentence.correctValue, sentence.answerValue);
						sentence.isCorrect = sentence.mistakes.length === 0 && sentence.answerValue.length > 0;
						console.log('sentence:', sentence);
						transcribed = true;
						}}
                />
			</div>
            {/if}
		</He>
	</div>
</div>
